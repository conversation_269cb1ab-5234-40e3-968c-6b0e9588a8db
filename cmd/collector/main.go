package main

import (
	"gitlab.insta360.com/duowenxuan/insta360-cmdb/internal/cloud"
	"gitlab.insta360.com/duowenxuan/insta360-cmdb/internal/collector"
	"gitlab.insta360.com/duowenxuan/insta360-cmdb/internal/config"
	"gitlab.insta360.com/duowenxuan/insta360-cmdb/internal/db"
	"gitlab.insta360.com/duowenxuan/insta360-cmdb/internal/utils"
)

func main() {
	// 1. 加载配置
	cfg := config.MustLoad("configs/config.yaml")

	// 2. 初始化日志
	utils.InitLogger(cfg.Logging)

	// 3. 初始化数据库
	storage := db.NewStorage(cfg.Database.Type)
	storage.Init(cfg.Database.DataSource)
	storage.Migrate()

	// 4. 创建采集控制器
	controller := collector.NewController(storage)

	// 5. 注册所有云账户
	for _, account := range cfg.Clouds {
		provider, err := new(cloud.ProviderFactory).Create(&account)
		if err != nil {
			utils.Errorf("Failed to create provider for account %s: %v", account.Name, err)
			continue
		}
		controller.RegisterProvider(provider)
		utils.Infof("Registered provider for account: %s (%s)", account.Name, account.Type)
	}

	// 6. 执行采集任务
	utils.Info("Starting collection process...")
	if err := controller.Run(); err != nil {
		utils.Fatalf("采集失败: %v", err)
	}
	utils.Info("Collection process completed successfully")
}
