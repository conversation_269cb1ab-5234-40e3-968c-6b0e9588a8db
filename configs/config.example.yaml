# Insta360 CMDB 资产采集器配置示例
# 复制此文件为 config.yaml 并修改相应的配置

database:
  type: mysql
  data_source: "cmdb_user:cmdb_password@tcp(127.0.0.1:3306)/cmdb?charset=utf8mb4&parseTime=True&loc=Local"
  max_open_conns: 10

cloud_accounts:
  # ==================== 阿里云账户配置 ====================
  
  # 生产环境阿里云账户 - 使用自动生成的 endpoint
  - type: aliyun
    name: aliyun-prod-account
    enabled: true
    access_key_id: YOUR_ALIYUN_ACCESS_KEY_ID
    access_secret: YOUR_ALIYUN_ACCESS_SECRET
    regions:
      - cn-shenzhen      # 深圳
      - cn-hangzhou      # 杭州
      - cn-beijing       # 北京
      - cn-shanghai      # 上海
    # endpoint 不指定，系统会自动为每个区域生成正确的 endpoint:
    # cn-shenzhen  -> ecs.cn-shenzhen.aliyuncs.com
    # cn-hangzhou  -> ecs.cn-hangzhou.aliyuncs.com
    # cn-beijing   -> ecs.cn-beijing.aliyuncs.com
    # cn-shanghai  -> ecs.cn-shanghai.aliyuncs.com

  # 测试环境阿里云账户 - 暂时禁用
  - type: aliyun
    name: aliyun-test-account
    enabled: false  # 暂时禁用测试环境采集
    access_key_id: YOUR_TEST_ALIYUN_ACCESS_KEY_ID
    access_secret: YOUR_TEST_ALIYUN_ACCESS_SECRET
    regions:
      - cn-hangzhou
    # 同样使用自动生成的 endpoint

  # 海外阿里云账户
  - type: aliyun
    name: aliyun-overseas-account
    enabled: true
    access_key_id: YOUR_OVERSEAS_ALIYUN_ACCESS_KEY_ID
    access_secret: YOUR_OVERSEAS_ALIYUN_ACCESS_SECRET
    regions:
      - us-west-1        # 美国西部
      - ap-southeast-1   # 新加坡
      - eu-central-1     # 德国法兰克福
    # 海外区域也会自动生成正确的 endpoint:
    # us-west-1      -> ecs.us-west-1.aliyuncs.com
    # ap-southeast-1 -> ecs.ap-southeast-1.aliyuncs.com
    # eu-central-1   -> ecs.eu-central-1.aliyuncs.com

  # 私有云阿里云账户 - 使用自定义 endpoint
  - type: aliyun
    name: aliyun-private-cloud
    enabled: false  # 根据需要启用
    access_key_id: YOUR_PRIVATE_CLOUD_ACCESS_KEY_ID
    access_secret: YOUR_PRIVATE_CLOUD_ACCESS_SECRET
    regions:
      - cn-private-1
      - cn-private-2
    endpoint: ecs.your-private-cloud.com  # 自定义 endpoint，所有区域都会使用这个

  # ==================== AWS 账户配置 ====================
  
  # AWS 美国东部账户
  - type: aws
    name: aws-us-east-account
    enabled: true
    access_key_id: YOUR_AWS_ACCESS_KEY_ID
    access_secret: YOUR_AWS_SECRET_ACCESS_KEY
    regions:
      - us-east-1
      - us-east-2

  # AWS 美国西部账户
  - type: aws
    name: aws-us-west-account
    enabled: true
    access_key_id: YOUR_AWS_WEST_ACCESS_KEY_ID
    access_secret: YOUR_AWS_WEST_SECRET_ACCESS_KEY
    regions:
      - us-west-1
      - us-west-2

  # AWS 欧洲账户
  - type: aws
    name: aws-eu-account
    enabled: false  # 暂时禁用欧洲区域采集
    access_key_id: YOUR_AWS_EU_ACCESS_KEY_ID
    access_secret: YOUR_AWS_EU_SECRET_ACCESS_KEY
    regions:
      - eu-west-1      # 爱尔兰
      - eu-central-1   # 德国法兰克福

  # AWS 亚太账户
  - type: aws
    name: aws-apac-account
    enabled: true
    access_key_id: YOUR_AWS_APAC_ACCESS_KEY_ID
    access_secret: YOUR_AWS_APAC_SECRET_ACCESS_KEY
    regions:
      - ap-southeast-1  # 新加坡
      - ap-northeast-1  # 东京

logging:
  level: info    # debug/info/warn/error
  file: /var/log/cmdb-collector.log

options:
  batch_size: 100      # 批量处理大小
  max_retries: 3       # 最大重试次数
  timeout_secs: 300    # 超时时间(秒)

# ==================== 配置说明 ====================
#
# 1. 阿里云 Endpoint 规则:
#    - 不指定 endpoint: 自动生成 ecs.[region].aliyuncs.com
#    - 指定 endpoint: 所有区域使用指定的 endpoint
#    - 推荐使用自动生成，除非是私有云环境
#
# 2. 多账户支持:
#    - 可以配置任意数量的阿里云和 AWS 账户
#    - 每个账户可以独立启用/禁用
#    - 每个账户可以配置不同的区域
#
# 3. 安全建议:
#    - 使用具有只读权限的 AK/SK
#    - 定期轮换访问密钥
#    - 妥善保管配置文件
#
# 4. 性能优化:
#    - 根据实际需求调整 batch_size
#    - 合理设置 timeout_secs
#    - 监控 API 调用频率限制
