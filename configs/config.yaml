database:
  type: mysql
  data_source: "cmdb_user:cmdb_password@tcp(127.0.0.1:3306)/cmdb?charset=utf8mb4&parseTime=True&loc=Local"
  max_open_conns: 10

cloud_accounts:
  - type: aliyun
    name: prod-account
    access_key_id: LTAI4FakeAccessKeyId123456
    access_secret: 6gcLFakeAccessSecret123456789012345
    regions:
      - cn-shenzhen
      - cn-hangzhou
    endpoint: ecs.aliyuncs.com

  - type: aws
    name: us-east-account
    access_key_id: AKIAFAKEACCESSKEYID12345
    access_secret: xj8kFakeSecretAccessKey123456789012345678
    regions:
      - us-east-1
      - us-west-2

logging:
  level: info
  file: /var/log/cmdb-collector.log

options:
  batch_size: 100
  max_retries: 3
  timeout_secs: 300
