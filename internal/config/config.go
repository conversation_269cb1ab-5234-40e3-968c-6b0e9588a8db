package config

import (
	"fmt"
	"os"

	"gopkg.in/yaml.v3"
)

type Config struct {
	Database DatabaseConfig   `yaml:"database"`
	Clouds   []CloudAccount   `yaml:"cloud_accounts"`
	Logging  LoggingConfig    `yaml:"logging"`
	Options  CollectorOptions `yaml:"options"`
}

type CloudAccount struct {
	Type         string   `yaml:"type"`               // aliyun/aws
	Name         string   `yaml:"name"`               // 账户别名
	AccessKeyID  string   `yaml:"access_key_id"`      // AK
	AccessSecret string   `yaml:"access_secret"`      // SK
	Regions      []string `yaml:"regions"`            // 需采集的区域列表
	Endpoint     string   `yaml:"endpoint,omitempty"` // 自定义Endpoint
}

type DatabaseConfig struct {
	Type         string `yaml:"type"`           // mysql/postgresql
	DataSource   string `yaml:"data_source"`    // DSN连接字符串
	MaxOpenConns int    `yaml:"max_open_conns"` // 最大连接数
}

type LoggingConfig struct {
	Level string `yaml:"level"` // debug/info/warn/error
	File  string `yaml:"file"`  // 日志文件路径
}

type CollectorOptions struct {
	BatchSize   int `yaml:"batch_size"`   // 批量处理大小
	MaxRetries  int `yaml:"max_retries"`  // 最大重试次数
	TimeoutSecs int `yaml:"timeout_secs"` // 超时时间(秒)
}

// MustLoad 加载配置文件，失败时panic
func MustLoad(configPath string) *Config {
	cfg, err := Load(configPath)
	if err != nil {
		panic(fmt.Sprintf("failed to load config: %v", err))
	}
	return cfg
}

// Load 加载配置文件
func Load(configPath string) (*Config, error) {
	data, err := os.ReadFile(configPath)
	if err != nil {
		return nil, fmt.Errorf("failed to read config file: %w", err)
	}

	var cfg Config
	if err := yaml.Unmarshal(data, &cfg); err != nil {
		return nil, fmt.Errorf("failed to parse config file: %w", err)
	}

	// 设置默认值
	if cfg.Options.BatchSize == 0 {
		cfg.Options.BatchSize = 100
	}
	if cfg.Options.MaxRetries == 0 {
		cfg.Options.MaxRetries = 3
	}
	if cfg.Options.TimeoutSecs == 0 {
		cfg.Options.TimeoutSecs = 300
	}
	if cfg.Database.MaxOpenConns == 0 {
		cfg.Database.MaxOpenConns = 10
	}

	return &cfg, nil
}
