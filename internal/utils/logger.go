package utils

import (
	"os"
	"path/filepath"

	"github.com/sirupsen/logrus"
	"gitlab.insta360.com/duowenxuan/insta360-cmdb/internal/config"
)

var logger *logrus.Logger

// InitLogger 初始化日志系统
func InitLogger(cfg config.LoggingConfig) error {
	logger = logrus.New()

	// 设置日志级别
	level, err := logrus.ParseLevel(cfg.Level)
	if err != nil {
		level = logrus.InfoLevel
	}
	logger.SetLevel(level)

	// 设置日志格式
	logger.SetFormatter(&logrus.TextFormatter{
		FullTimestamp:   true,
		TimestampFormat: "2006-01-02 15:04:05",
	})

	// 设置日志输出
	if cfg.File != "" {
		// 确保日志目录存在
		if err := os.MkdirAll(filepath.Dir(cfg.File), 0755); err != nil {
			return err
		}

		file, err := os.OpenFile(cfg.File, os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0666)
		if err != nil {
			return err
		}
		logger.SetOutput(file)
	} else {
		logger.SetOutput(os.Stdout)
	}

	logger.Info("Logger initialized successfully")
	return nil
}

// Logger 获取日志实例
func Logger() *logrus.Logger {
	if logger == nil {
		// 如果没有初始化，使用默认配置
		logger = logrus.New()
		logger.SetLevel(logrus.InfoLevel)
		logger.SetFormatter(&logrus.TextFormatter{
			FullTimestamp:   true,
			TimestampFormat: "2006-01-02 15:04:05",
		})
	}
	return logger
}

// 便捷方法
func Debug(args ...interface{}) {
	Logger().Debug(args...)
}

func Debugf(format string, args ...interface{}) {
	Logger().Debugf(format, args...)
}

func Info(args ...interface{}) {
	Logger().Info(args...)
}

func Infof(format string, args ...interface{}) {
	Logger().Infof(format, args...)
}

func Warn(args ...interface{}) {
	Logger().Warn(args...)
}

func Warnf(format string, args ...interface{}) {
	Logger().Warnf(format, args...)
}

func Error(args ...interface{}) {
	Logger().Error(args...)
}

func Errorf(format string, args ...interface{}) {
	Logger().Errorf(format, args...)
}

func Fatal(args ...interface{}) {
	Logger().Fatal(args...)
}

func Fatalf(format string, args ...interface{}) {
	Logger().Fatalf(format, args...)
}
