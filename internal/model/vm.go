package model

// 云服务器基础模型
type VirtualMachine struct {
	ID             string                 `json:"id" gorm:"primaryKey"`
	Name           string                 `json:"name"`
	CloudProvider  string                 `json:"cloud_provider"` // 云厂商类型: aliyun/aws
	Region         string                 `json:"region"`
	Zone           string                 `json:"zone,omitempty"`
	Status         string                 `json:"status"` // running/stopped/...
	InstanceType   string                 `json:"instance_type"`
	CPU            int                    `json:"cpu"`
	Memory         int                    `json:"memory"`
	OSType         string                 `json:"os_type"` // windows/linux
	VpcID          string                 `json:"vpc_id,omitempty"`
	SecurityGroups []string               `json:"security_groups"`
	PublicIP       string                 `json:"public_ip,omitempty"`
	PrivateIP      string                 `json:"private_ip,omitempty"`
	Tags           map[string]string      `json:"tags"`
	RawData        map[string]interface{} `json:"raw_data,omitempty"` // 保留原始数据
	LastUpdated    int64                  `json:"last_updated"`       // 时间戳
}
